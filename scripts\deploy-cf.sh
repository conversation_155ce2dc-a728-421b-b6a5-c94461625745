#!/bin/bash

# Build the OpenNext application
opennextjs-cloudflare build --skipWranglerConfigCheck

# Navigate to the build directory
cd .open-next

# Create wrangler.toml in the build directory
cat > wrangler.toml << 'EOF'
name = "bratgenerator"
main = "worker.js"
compatibility_date = "2025-03-01"
compatibility_flags = ["nodejs_compat", "global_fetch_strictly_public"]

[assets]
binding = "ASSETS"
directory = "assets"

[observability]
enabled = true

[vars]
# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "https://bratgenerator.casa"
NEXT_PUBLIC_PROJECT_NAME = "Brat Generator"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
DATABASE_URL = "*************************************************************************************"

# -----------------------------------------------------------------------------
# Auth with next-auth
# -----------------------------------------------------------------------------
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="
AUTH_URL = "http://localhost:3000/api/auth"
AUTH_TRUST_HOST = true

# Google Auth
AUTH_GOOGLE_ID = "662279989381-59n96c1hr8io6m7kevpamfhlc85e7694.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "662279989381-59n96c1hr8io6m7kevpamfhlc85e7694.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "true"

# Github Auth
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = "G-GS1DLYMTHL"

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# Analytics with Plausible
NEXT_PUBLIC_PLAUSIBLE_DOMAIN = "bratgenerator.casa"
NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL = "https://plausible.nancook.com/js/script.file-downloads.hash.outbound-links.pageview-props.revenue.tagged-events.js"

# -----------------------------------------------------------------------------
# Payment with Stripe
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

# -----------------------------------------------------------------------------
# Payment with Creem
# -----------------------------------------------------------------------------
CREEM_ENV = ""
CREEM_API_KEY = ""
CREEM_WEBHOOK_SECRET = ""
CREEM_PRODUCTS = ''

PAY_PROVIDER = "stripe"

NEXT_PUBLIC_PAY_SUCCESS_URL = "/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "/pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "/pricing"

NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = "<EMAIL>"

NEXT_PUBLIC_DEFAULT_THEME = "system"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""

# Google Adsence Code
NEXT_PUBLIC_GOOGLE_ADCODE = ""
EOF

# Deploy using wrangler
if [ "$1" = "--dry-run" ]; then
    wrangler deploy worker.js --dry-run
else
    wrangler deploy worker.js
fi
